# GitHub Actions 代理获取验证工具

## 项目简介

这是一个专门为GitHub Actions设计的代理获取和验证工具，能够自动从多个代理源获取代理列表，实时验证代理的可用性，并将验证通过的代理保存到文件中。

## 功能特性

- 🔍 **多源代理获取**: 从多个GitHub代理源自动获取最新代理列表
- ⚡ **并发验证**: 使用多线程并发验证代理可用性，提高验证效率
- 📊 **实时统计**: 实时显示验证进度和结果统计
- 💾 **结果保存**: 自动保存验证通过的代理到txt文件
- 🕐 **定时执行**: 每12小时自动运行一次，保持代理列表更新
- 📈 **详细日志**: 完整的控制台日志输出，便于监控和调试

## 项目结构

```
GitHub代理验证工具/
├── 代理获取验证器.py          # 主程序文件
├── requirements.txt           # Python依赖文件
├── README.md                 # 项目说明文档
├── .github/
│   └── workflows/
│       └── 代理验证工作流.yml  # GitHub Actions工作流配置
└── 验证通过的代理.txt         # 验证结果文件（自动生成）
```

## 使用方法

### 本地运行

1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 运行程序：
```bash
python 代理获取验证器.py
```

### GitHub Actions自动运行

1. 将项目推送到GitHub仓库
2. GitHub Actions会自动按照以下时间表运行：
   - 每12小时执行一次（UTC时间 00:00 和 12:00）
   - 推送到main/master分支时触发
   - 支持手动触发

## 代理源

程序从以下GitHub代理源获取代理列表：

- TheSpeedX/PROXY-List
- clarketm/proxy-list  
- sunny9577/proxy-scraper
- ShiftyTR/Proxy-List
- monosans/proxy-list

## 验证机制

- **连接测试**: 测试代理是否能够正常连接
- **响应时间**: 记录代理的响应时间
- **内容验证**: 验证代理返回的响应内容是否正确
- **超时设置**: 10秒连接超时，确保验证效率

## 输出格式

验证通过的代理保存格式：
```
# GitHub Actions 代理验证结果
# 验证时间: 2024-01-01 12:00:00
# 总计测试: 1000 个代理
# 验证通过: 50 个代理
# 成功率: 5.00%
# ==========================================

1.2.3.4:8080 # 响应时间: 1234.56ms
5.6.7.8:3128 # 响应时间: 987.65ms
...
```

## 技术规范

- **编程语言**: Python 3.11+
- **并发处理**: ThreadPoolExecutor (默认50个并发线程)
- **网络请求**: requests库
- **编码规范**: 
  - 文件名使用中文
  - 代码变量/函数/类名使用英文
  - 输出信息和注释使用中文

## 监控和日志

GitHub Actions提供详细的执行日志，包括：

- 📡 代理获取进度
- ✅ 验证结果统计  
- 🕐 执行时间信息
- 📊 成功/失败代理数量
- 💾 文件保存状态

## 许可证

本项目采用MIT许可证。
