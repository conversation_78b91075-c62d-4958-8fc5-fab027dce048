#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GitHub Actions 代理获取和验证工具
功能：自动获取代理列表，验证可用性，保存到文件
"""

import requests
import time
import threading
import re
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime
import os
import sys


class ProxyValidator:
    """代理验证器类"""
    
    def __init__(self):
        self.valid_proxies = []
        self.total_proxies = 0
        self.tested_count = 0
        self.valid_count = 0
        self.start_time = None
        
    def fetch_proxy_sources(self):
        """从多个源获取代理列表"""
        print("🔍 开始获取代理列表...")
        
        proxy_sources = [
            "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/http.txt",
            "https://raw.githubusercontent.com/clarketm/proxy-list/master/proxy-list-raw.txt",
            "https://raw.githubusercontent.com/sunny9577/proxy-scraper/master/proxies.txt",
            "https://raw.githubusercontent.com/ShiftyTR/Proxy-List/master/http.txt",
            "https://raw.githubusercontent.com/monosans/proxy-list/main/proxies/http.txt",
        ]
        
        all_proxies = set()
        
        for i, source in enumerate(proxy_sources, 1):
            try:
                print(f"📡 正在获取代理源 {i}/{len(proxy_sources)}: {source}")
                response = requests.get(source, timeout=30)
                response.raise_for_status()
                
                # 提取IP:PORT格式的代理
                proxy_pattern = r'\b(?:\d{1,3}\.){3}\d{1,3}:\d{1,5}\b'
                proxies = re.findall(proxy_pattern, response.text)
                
                before_count = len(all_proxies)
                all_proxies.update(proxies)
                new_count = len(all_proxies) - before_count
                
                print(f"✅ 源 {i} 获取成功，新增 {new_count} 个代理")
                
            except Exception as e:
                print(f"❌ 源 {i} 获取失败: {str(e)}")
                continue
        
        proxy_list = list(all_proxies)
        self.total_proxies = len(proxy_list)
        print(f"📊 总共获取到 {self.total_proxies} 个唯一代理")
        
        return proxy_list
    
    def test_proxy(self, proxy):
        """测试单个代理的可用性"""
        try:
            proxy_dict = {
                'http': f'http://{proxy}',
                'https': f'http://{proxy}'
            }
            
            # 测试连接性和响应时间
            test_urls = [
                'http://httpbin.org/ip',
                'https://api.ipify.org?format=json'
            ]
            
            start_time = time.time()
            
            for test_url in test_urls:
                response = requests.get(
                    test_url,
                    proxies=proxy_dict,
                    timeout=10,
                    headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
                )
                
                if response.status_code == 200:
                    response_time = round((time.time() - start_time) * 1000, 2)
                    
                    # 验证响应内容
                    if 'origin' in response.text or 'ip' in response.text:
                        self.tested_count += 1
                        self.valid_count += 1
                        
                        proxy_info = f"{proxy} # 响应时间: {response_time}ms"
                        self.valid_proxies.append(proxy_info)
                        
                        print(f"✅ 代理有效: {proxy} (响应时间: {response_time}ms) [{self.tested_count}/{self.total_proxies}]")
                        return True
            
            self.tested_count += 1
            print(f"❌ 代理无效: {proxy} [{self.tested_count}/{self.total_proxies}]")
            return False
            
        except Exception as e:
            self.tested_count += 1
            print(f"❌ 代理测试失败: {proxy} - {str(e)} [{self.tested_count}/{self.total_proxies}]")
            return False
    
    def validate_proxies(self, proxy_list, max_workers=50):
        """并发验证代理列表"""
        print(f"\n🔧 开始验证代理，使用 {max_workers} 个并发线程...")
        self.start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_proxy = {
                executor.submit(self.test_proxy, proxy): proxy 
                for proxy in proxy_list
            }
            
            # 等待所有任务完成
            for future in as_completed(future_to_proxy):
                try:
                    future.result()
                except Exception as e:
                    proxy = future_to_proxy[future]
                    print(f"❌ 代理验证异常: {proxy} - {str(e)}")
    
    def save_valid_proxies(self, filename="验证通过的代理.txt"):
        """保存验证通过的代理到文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"# GitHub Actions 代理验证结果\n")
                f.write(f"# 验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"# 总计测试: {self.total_proxies} 个代理\n")
                f.write(f"# 验证通过: {self.valid_count} 个代理\n")
                f.write(f"# 成功率: {(self.valid_count/self.total_proxies*100):.2f}%\n")
                f.write(f"# ==========================================\n\n")
                
                for proxy in self.valid_proxies:
                    f.write(f"{proxy}\n")
            
            print(f"💾 验证通过的代理已保存到: {filename}")
            
        except Exception as e:
            print(f"❌ 保存文件失败: {str(e)}")
    
    def print_summary(self):
        """打印验证结果摘要"""
        if self.start_time:
            elapsed_time = time.time() - self.start_time
            
            print(f"\n{'='*60}")
            print(f"📊 验证结果摘要")
            print(f"{'='*60}")
            print(f"🕐 执行时间: {elapsed_time:.2f} 秒")
            print(f"📈 总计测试: {self.total_proxies} 个代理")
            print(f"✅ 验证通过: {self.valid_count} 个代理")
            print(f"❌ 验证失败: {self.total_proxies - self.valid_count} 个代理")
            print(f"📊 成功率: {(self.valid_count/self.total_proxies*100):.2f}%")
            print(f"{'='*60}")


def main():
    """主函数"""
    print("🚀 GitHub Actions 代理获取验证工具启动")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    validator = ProxyValidator()
    
    try:
        # 获取代理列表
        proxy_list = validator.fetch_proxy_sources()
        
        if not proxy_list:
            print("❌ 未获取到任何代理，程序退出")
            sys.exit(1)
        
        # 验证代理
        validator.validate_proxies(proxy_list)
        
        # 保存结果
        validator.save_valid_proxies()
        
        # 打印摘要
        validator.print_summary()
        
        print("🎉 代理验证任务完成！")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 程序执行出错: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
