name: 代理获取验证工具

on:
  # 定时执行：每12小时运行一次
  schedule:
    - cron: '0 */12 * * *'  # 每12小时执行一次
  
  # 手动触发
  workflow_dispatch:
  
  # 推送到main分支时触发
  push:
    branches: [ main, master ]

jobs:
  proxy-validation:
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 检出代码
      uses: actions/checkout@v4
      
    - name: 🐍 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        
    - name: 📦 安装依赖
      run: |
        echo "🔧 正在安装Python依赖..."
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        echo "✅ 依赖安装完成"
        
    - name: 🕐 显示执行时间
      run: |
        echo "⏰ 工作流开始时间: $(date '+%Y-%m-%d %H:%M:%S UTC')"
        echo "🌍 时区信息: $(timedatectl show --property=Timezone --value 2>/dev/null || echo 'UTC')"
        
    - name: 🔍 执行代理获取和验证
      run: |
        echo "🚀 开始执行代理获取验证程序..."
        echo "================================================"
        python 代理获取验证器.py
        echo "================================================"
        echo "✅ 代理验证程序执行完成"
        
    - name: 📊 显示验证结果统计
      run: |
        echo "📈 验证结果文件信息:"
        if [ -f "验证通过的代理.txt" ]; then
          echo "✅ 验证通过的代理文件已生成"
          echo "📄 文件大小: $(du -h 验证通过的代理.txt | cut -f1)"
          echo "📝 文件行数: $(wc -l < 验证通过的代理.txt)"
          echo ""
          echo "📋 文件前10行内容预览:"
          echo "----------------------------------------"
          head -10 验证通过的代理.txt
          echo "----------------------------------------"
          echo ""
          echo "🔢 有效代理统计:"
          # 统计非注释行数量（排除以#开头的行）
          valid_count=$(grep -v '^#' 验证通过的代理.txt | grep -v '^$' | wc -l)
          echo "✅ 验证通过的代理数量: $valid_count"
        else
          echo "❌ 验证通过的代理文件未找到"
        fi
        
    - name: 📤 上传验证结果
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: proxy-validation-results-${{ github.run_number }}
        path: |
          验证通过的代理.txt
        retention-days: 30
        
    - name: 💾 提交验证结果到仓库
      if: success()
      run: |
        echo "🔄 准备提交验证结果到仓库..."
        
        # 配置Git用户信息
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        
        # 检查是否有文件变更
        if [ -f "验证通过的代理.txt" ]; then
          # 添加文件到Git
          git add 验证通过的代理.txt
          
          # 检查是否有变更需要提交
          if git diff --staged --quiet; then
            echo "📝 没有新的变更需要提交"
          else
            # 提交变更
            commit_time=$(date '+%Y-%m-%d %H:%M:%S UTC')
            git commit -m "🤖 自动更新代理验证结果 - $commit_time"
            
            # 推送到仓库
            git push
            echo "✅ 验证结果已成功提交到仓库"
          fi
        else
          echo "❌ 验证结果文件不存在，跳过提交"
        fi
        
    - name: 🕐 显示完成时间
      if: always()
      run: |
        echo "⏰ 工作流完成时间: $(date '+%Y-%m-%d %H:%M:%S UTC')"
        echo "🎉 代理验证工作流执行完成！"
        
    - name: 📋 执行摘要
      if: always()
      run: |
        echo ""
        echo "================================================"
        echo "📊 GitHub Actions 执行摘要"
        echo "================================================"
        echo "🔧 工作流名称: 代理获取验证工具"
        echo "🏃 运行编号: ${{ github.run_number }}"
        echo "🌿 分支: ${{ github.ref_name }}"
        echo "👤 触发者: ${{ github.actor }}"
        echo "📅 执行日期: $(date '+%Y-%m-%d')"
        echo "⏰ 执行时间: $(date '+%H:%M:%S UTC')"
        echo "🔄 触发方式: ${{ github.event_name }}"
        echo "================================================"
